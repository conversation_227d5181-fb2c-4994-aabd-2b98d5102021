from flask import Flask, request


app = Flask(__name__)

@app.route('/', methods=['POST', 'GET'])
def index():
    # Get request headers
    headers = dict(request.headers)
    print("Request Headers:")
    for key, value in headers.items():
        print(f"{key}: {value}")

    # Get request body
    data = request.get_data(as_text=True)
    print("Request Body:")
    print(data)

    # Return request body
    return data

@app.route('/ping', methods=['GET'])
def ping():
    return 'pong'

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5555)
