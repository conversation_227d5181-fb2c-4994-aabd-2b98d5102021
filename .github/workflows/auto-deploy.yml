name: "🤖 | Deploy"

on:
  push:
    branches:
      - "release/development"
      - "release/qa"
      - "release/production"
      - "release/ril-production"
      - "main"

env:
  MODULE_ID: sample_flask

jobs:
  deploy:
    # if this workflow is running from a SenseHawk repo
    if: ${{ startsWith(github.repository, 'sensehawk/') }}
    uses: sensehawk/workflows/.github/workflows/deploy.yml@main
    secrets: inherit

  deploy-external:
    # if this workflow is running from a NXT repo (should only run for the main branch)
    if: ${{ !startsWith(github.repository, 'sensehawk/') && github.ref == 'refs/heads/main' }}
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Set module name
        id: module-id
        run: |
          echo "module-id=${{ env.MODULE_ID }}" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get short SHA
        id: get_sha
        run: |
          echo "Full SHA: ${GITHUB_SHA}"
          SHORT_SHA=$(echo ${GITHUB_SHA} | cut -c1-7)
          echo "Short SHA: ${SHORT_SHA}"
          echo "sha7=${SHORT_SHA}" >> $GITHUB_OUTPUT

      # Docker build and push steps (SIMULATED)
      - name: Determine Docker image name
        id: image-name
        run: |
          MODULE=${{ steps.module-id.outputs.module-id }}
          IMAGE_NAME="nextracker/${MODULE}"
          echo "name=${IMAGE_NAME}" >> $GITHUB_OUTPUT
          echo "🐳 Docker image name determined: ${IMAGE_NAME}"

      # COMMENTED OUT: Actual Docker login
      # - name: Login to DockerHub
      #   uses: docker/login-action@v3
      #   with:
      #     username: ${{ secrets.NXT_DOCKER_USERNAME }}
      #     password: ${{ secrets.NXT_DOCKER_TOKEN }}

      # SIMULATION: Docker login
      - name: Simulate Docker login
        run: |
          echo "🔐 SIMULATION: Docker login successful"
          echo "✅ Authenticated to DockerHub (simulated)"

      # COMMENTED OUT: Actual Docker Buildx setup
      # - name: Set up Docker Buildx
      #   uses: docker/setup-buildx-action@v3

      # SIMULATION: Docker Buildx setup
      - name: Simulate Docker Buildx setup
        run: |
          echo "🔧 SIMULATION: Docker Buildx setup complete"

      # COMMENTED OUT: Actual Docker build and push
      # - name: Build and push Docker image
      #   id: docker_build
      #   uses: docker/build-push-action@v6
      #   with:
      #     context: .
      #     push: true
      #     tags: ${{ steps.image-name.outputs.name }}:${{ steps.get_sha.outputs.sha7 }}
      #     build-args: |
      #       DD_GIT_REPOSITORY_URL=${{ github.server_url }}/${{ github.repository }}
      #       DD_GIT_COMMIT_SHA=${{ github.sha }}
      #       DISABLE_DDTRACE=false
      #     labels: |
      #       com.datadog.git.repository_url=${{ github.server_url }}/${{ github.repository }}
      #       com.datadog.git.commit_sha=${{ github.sha }}

      # SIMULATION: Docker build and push
      - name: Simulate Docker build and push
        id: docker_build
        run: |
          IMAGE_TAG="${{ steps.image-name.outputs.name }}:${{ steps.get_sha.outputs.sha7 }}"
          echo "🏗️ SIMULATION: Building Docker image..."
          echo "📦 Image tag: ${IMAGE_TAG}"
          echo "🚀 SIMULATION: Pushing to DockerHub..."
          echo "✅ Docker image built and pushed successfully (simulated)"

          # Simulate digest output for compatibility
          FAKE_DIGEST="sha256:$(echo -n "${IMAGE_TAG}" | sha256sum | cut -d' ' -f1)"
          echo "digest=${FAKE_DIGEST}" >> $GITHUB_OUTPUT
          echo "📋 Image digest: ${FAKE_DIGEST}"

      # SIMULATION: Image digest output
      - name: Simulate image digest output
        run: |
          echo "📋 Docker image digest: ${{ steps.docker_build.outputs.digest }}"

      # AWS Lambda deployment (SIMULATED)
      # COMMENTED OUT: Actual AWS credentials configuration
      # - name: Configure AWS credentials
      #   uses: aws-actions/configure-aws-credentials@v3
      #   with:
      #     aws-access-key-id: ${{ secrets.NXT_AWS_LAMBDA_ACCESS_KEY }}
      #     aws-secret-access-key: ${{ secrets.NXT_AWS_LAMBDA_SECRET_ACCESS_KEY }}
      #     aws-region: "us-east-1"

      # SIMULATION: AWS credentials configuration
      - name: Simulate AWS credentials configuration
        run: |
          echo "🔐 SIMULATION: AWS credentials configured"
          echo "📍 Region: us-east-1 (simulated)"

      # COMMENTED OUT: Actual Lambda function invocation
      # - name: Invoke Lambda function
      #   id: invoke_lambda_function
      #   run: |
      #     RESPONSE=$(aws lambda invoke \
      #       --function-name "nxt-deploy_nomad_job-lambda" \
      #       --cli-binary-format raw-in-base64-out \
      #       --payload '{"job": "${{ steps.module-id.outputs.module-id }}", "hash": "${{ steps.get_sha.outputs.sha7 }}"}' \
      #       response.json)

      # SIMULATION: Lambda function invocation
      - name: Simulate Lambda function invocation
        id: invoke_lambda_function
        run: |
          echo "🚀 SIMULATION: Invoking Lambda function..."

          # Simulate the Lambda response
          FAKE_EVAL_ID="eval-$(date +%s)-$(echo $RANDOM | md5sum | head -c 8)"
          STATUS_CODE="200"
          WARNINGS=""
          WARNINGS_MSG="None"

          # Create simulated response body
          RESPONSE_BODY='{
            "statusCode": 200,
            "body": {
              "EvalID": "'${FAKE_EVAL_ID}'",
              "Warnings": null,
              "Message": "Deployment simulation successful"
            }
          }'

          echo "📋 Simulated Lambda response generated"

          # Create a summary of the lambda invocation (same format as original)
          echo "## Lambda Invocation Summary (SIMULATED)" >> $GITHUB_STEP_SUMMARY
          echo "### Function Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Function:** nxt-deploy_nomad_job-lambda" >> $GITHUB_STEP_SUMMARY
          echo "- **Job Name:** ${{ steps.module-id.outputs.module-id }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit:** ${{ steps.get_sha.outputs.sha7 }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Region:** us-east-1" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Response Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Status Code:** ${STATUS_CODE}" >> $GITHUB_STEP_SUMMARY
          echo "- **Evaluation ID:** ${FAKE_EVAL_ID}" >> $GITHUB_STEP_SUMMARY
          echo "- **Warnings:** ${WARNINGS_MSG}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "⚠️ **NOTE: This is a simulated deployment for testing purposes**" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "<details><summary><strong>Simulated Response</strong></summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo "$RESPONSE_BODY" | jq '.' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY

          # Set the output for other steps to use if needed
          echo "response=$RESPONSE_BODY" >> $GITHUB_OUTPUT
          echo "eval_id=$FAKE_EVAL_ID" >> $GITHUB_OUTPUT
          echo "status_code=$STATUS_CODE" >> $GITHUB_OUTPUT

      - name: Log simulated output to console
        run: |
          echo "✅ SIMULATION COMPLETE"
          echo "Status Code: ${{ steps.invoke_lambda_function.outputs.status_code }}"
          echo "Evaluation ID: ${{ steps.invoke_lambda_function.outputs.eval_id }}"
          echo "🎯 Workflow simulation successful - ready for testing!"
