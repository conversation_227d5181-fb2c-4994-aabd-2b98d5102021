name: "🤖 | Deploy"

on:
  push:
    branches:
      - "release/development"
      - "release/qa"
      - "release/production"
      - "release/ril-production"
      - "main"

env:
  MODULE_ID: sample_flask

jobs:
  deploy:
    # if this workflow is running from a SenseHawk repo
    if: ${{ startsWith(github.repository, 'sensehawk/') }}
    uses: sensehawk/workflows/.github/workflows/deploy.yml@main
    secrets: inherit

  deploy-external:
    # if this workflow is running from a NXT repo (should only run for the main branch)
    if: ${{ !startsWith(github.repository, 'sensehawk/') && github.ref == 'refs/heads/main' }}
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Set module name
        id: module-id
        run: |
          echo "module-id=${{ env.MODULE_ID }}" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v4

      # Docker build and push steps
      - name: Determine Docker image name
        id: image-name
        run: |
          MODULE=${{ steps.module-id.outputs.module-id }}
          IMAGE_NAME="nextracker/${MODULE}"
          echo "name=${IMAGE_NAME}" >> $GITHUB_OUTPUT

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.NXT_DOCKER_USERNAME }}
          password: ${{ secrets.NXT_DOCKER_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ steps.image-name.outputs.name }}:${{ steps.get_sha.outputs.sha7 }}
          build-args: |
            DD_GIT_REPOSITORY_URL=${{ github.server_url }}/${{ github.repository }}
            DD_GIT_COMMIT_SHA=${{ github.sha }}
            DISABLE_DDTRACE=false
          labels: |
            com.datadog.git.repository_url=${{ github.server_url }}/${{ github.repository }}
            com.datadog.git.commit_sha=${{ github.sha }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

      # AWS Lambda deployment
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.NXT_AWS_LAMBDA_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.NXT_AWS_LAMBDA_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"

      - name: Invoke Lambda function
        id: invoke_lambda_function
        run: |
          RESPONSE=$(aws lambda invoke \
            --function-name "nxt-deploy_nomad_job-lambda" \
            --cli-binary-format raw-in-base64-out \
            --payload '{"job": "${{ steps.module-id.outputs.module-id }}", "hash": "${{ steps.get_sha.outputs.sha7 }}"}' \
            response.json)

          # Get the response body
          RESPONSE_BODY=$(cat response.json)

          # Parse and format the evaluation details
          EVAL_ID=$(echo $RESPONSE_BODY | jq -r '.body.EvalID')
          STATUS_CODE=$(echo $RESPONSE_BODY | jq -r '.statusCode')
          WARNINGS=$(echo $RESPONSE_BODY | jq -r '.body.Warnings')
          WARNINGS_MSG=$([ -z "$WARNINGS" ] && echo "None" || echo "$WARNINGS")

          # Create a summary of the lambda invocation
          echo "## Lambda Invocation Summary" >> $GITHUB_STEP_SUMMARY
          echo "### Function Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Function:** nxt-deploy_nomad_job-lambda" >> $GITHUB_STEP_SUMMARY
          echo "- **Job Name:** ${{ steps.module-id.outputs.module-id }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit:** ${{ steps.get_sha.outputs.sha7 }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Region:** us-east-1" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Response Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Status Code:** ${STATUS_CODE}" >> $GITHUB_STEP_SUMMARY
          echo "- **Evaluation ID:** ${EVAL_ID}" >> $GITHUB_STEP_SUMMARY
          echo "- **Warnings:** ${WARNINGS_MSG}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "<details><summary><strong>Raw Response</strong></summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo "$RESPONSE_BODY" | jq '.' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY

          # Set the output for other steps to use if needed
          echo "response=$RESPONSE_BODY" >> $GITHUB_OUTPUT
          echo "eval_id=$EVAL_ID" >> $GITHUB_OUTPUT
          echo "status_code=$STATUS_CODE" >> $GITHUB_OUTPUT

      - name: Log output to console
        run: |
          echo "Status Code: ${{ steps.invoke_lambda_function.outputs.status_code }}"
          echo "Evaluation ID: ${{ steps.invoke_lambda_function.outputs.eval_id }}"
