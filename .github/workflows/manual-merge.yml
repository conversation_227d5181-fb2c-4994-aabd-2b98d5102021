name: "👆 | Merge"

on:
  workflow_dispatch:
    inputs:
      source_branch:
        description: 'Source Branch'
        required: true
        type: choice
        options:
          - main
          - release/development
      target_branch:
        description: 'Target Branch'
        required: true
        type: choice
        options:
          - release/development
          - release/qa
      confirm:
        description: '⚠️ Merge will be performed immediately. Type "confirm" to proceed.'
        required: true

jobs:
  call-merge:
    if: github.event.inputs.confirm == 'confirm'
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: ${{ github.event.inputs.source_branch }}
      target_branch: ${{ github.event.inputs.target_branch }}
