name: "👆 | Release"

on:
  workflow_dispatch:
    inputs:
      sprint:
        description: Sprint number
      folder:
        description: Folder number
      target:
        type: choice
        description: Target
        options:
          - Production
          - RIL Production

jobs:
  create-release-pr:
    uses: sensehawk/workflows/.github/workflows/create-release-pr.yml@main
    secrets: inherit
    permissions:
      issues: write
      contents: read
      pull-requests: write
    with:
      sprint: ${{ github.event.inputs.sprint }}
      folder: ${{ github.event.inputs.folder }}
      target: ${{ github.event.inputs.target }}
